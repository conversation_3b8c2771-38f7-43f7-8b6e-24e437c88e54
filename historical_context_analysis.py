#!/usr/bin/env python3
"""
🏴‍☠️ TREASURE HUNT - ANALYSE DU CONTEXTE HISTORIQUE
Recherche de clés basées sur des événements historiques de 2013 et références Nintendo
"""

import hashlib
import datetime
import re

# Notre clé publique cible
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_hex_as_key(hex_key):
    """Teste une clé hex avec secp256k1"""
    try:
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        
        # Convertir en bytes
        private_key_bytes = bytes.fromhex(hex_key)
        
        # Vérifier que c'est dans la plage valide pour secp256k1
        private_key_int = int(hex_key, 16)
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Gén<PERSON>rer la clé publique
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Format non compressé (04 + x + y)
        public_key_hex = "04" + vk.to_string().hex()
        
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉🎉🎉 CLÉ PRIVÉE TROUVÉE! 🎉🎉🎉")
            print(f"Clé privée: {hex_key}")
            print(f"Clé publique: {public_key_hex}")
            return True
            
    except Exception as e:
        pass
    
    return False

def test_candidate(candidate, description):
    """Teste un candidat comme clé privée"""
    print(f"🔍 Test: {description} -> {candidate}")
    
    # Tester directement si c'est un hex de 64 caractères
    if len(candidate) == 64 and all(c in '0123456789abcdef' for c in candidate.lower()):
        if test_hex_as_key(candidate.lower()):
            return True
    
    # Tester comme hash SHA256
    hash_result = hashlib.sha256(candidate.encode()).hexdigest()
    if test_hex_as_key(hash_result):
        return True
    
    # Tester avec padding
    if len(candidate) < 64:
        padded = candidate.ljust(64, '0')
        if test_hex_as_key(padded):
            return True
    
    return False

def analyze_nintendo_context():
    """Analyse le contexte Nintendo de 2013"""
    print("🎮 ANALYSE DU CONTEXTE NINTENDO 2013")
    print("=" * 40)
    
    # Événements Nintendo importants de 2013
    nintendo_events_2013 = [
        # Dates importantes
        "20130522",  # 22 mai 2013 (référence Nikkei)
        "20130526",  # 26 mai 2013 (build date)
        "20130611",  # E3 2013 Nintendo Direct
        "20131120",  # Sortie Wii U
        "20131122",  # Sortie Zelda Wind Waker HD
        
        # Produits Nintendo 2013
        "WiiU",
        "3DS",
        "Nintendo3DS",
        "ZeldaWindWaker",
        "SuperMario3DWorld",
        "MarioKart8",
        "PikminThree",
        
        # Références techniques Nintendo
        "Nintendo",
        "Nintondo",  # Le message du genesis
        "NintendoNetwork",
        "Miiverse",
        
        # Codes produits Nintendo
        "NTR",  # Nintendo DS
        "CTR",  # Nintendo 3DS
        "WUP",  # Wii U
        
        # Personnages Nintendo
        "Mario",
        "Luigi",
        "Zelda",
        "Link",
        "Pikachu",
        "Samus",
        
        # Développeurs clés
        "Miyamoto",
        "Iwata",
        "Aonuma",
        "Sakurai"
    ]
    
    for event in nintendo_events_2013:
        test_candidate(event, f"Événement Nintendo: {event}")
        
        # Variations avec années
        test_candidate(f"{event}2013", f"Événement + 2013: {event}")
        test_candidate(f"2013{event}", f"2013 + Événement: {event}")
        
        # Variations en hex
        try:
            hex_event = event.encode().hex()
            test_candidate(hex_event, f"Hex de {event}")
        except:
            pass

def analyze_crypto_context_2013():
    """Analyse le contexte crypto de 2013"""
    print("\n₿ ANALYSE DU CONTEXTE CRYPTO 2013")
    print("=" * 35)
    
    crypto_events_2013 = [
        # Dates importantes crypto
        "20130403",  # Bitcoin atteint 100$
        "20130410",  # Bitcoin crash de 266$ à 50$
        "20131201",  # Bitcoin atteint 1000$
        "20131206",  # Dogecoin lancé
        "20131208",  # Litecoin pic
        
        # Événements techniques
        "Satoshi",
        "SatoshiNakamoto",
        "BitcoinFoundation",
        "MtGox",
        "Blockchain",
        "ProofOfWork",
        "SHA256",
        "ECDSA",
        "secp256k1",
        
        # Références Dogecoin
        "Doge",
        "SuchWow",
        "MuchCoin",
        "VeryMoney",
        "ToTheMoon",
        "Shiba",
        "ShibaInu",
        
        # Références techniques spécifiques
        "scrypt",
        "Litecoin",
        "P2P",
        "Cryptocurrency"
    ]
    
    for event in crypto_events_2013:
        test_candidate(event, f"Événement crypto: {event}")

def analyze_japanese_context():
    """Analyse le contexte japonais (Nikkei, Nintendo)"""
    print("\n🇯🇵 ANALYSE DU CONTEXTE JAPONAIS")
    print("=" * 32)
    
    japanese_context = [
        # Références Nikkei
        "Nikkei",
        "NikkeiIndex",
        "TokyoStockExchange",
        "TSE",
        "Yen",
        "JPY",
        
        # Villes japonaises
        "Tokyo",
        "Kyoto",
        "Osaka",
        "Hiroshima",
        "Nagoya",
        
        # Entreprises japonaises
        "Sony",
        "Honda",
        "Toyota",
        "Panasonic",
        "Toshiba",
        "Fujitsu",
        
        # Culture japonaise
        "Samurai",
        "Ninja",
        "Karate",
        "Sushi",
        "Manga",
        "Anime",
        
        # Termes techniques japonais
        "Kaizen",
        "Tsunami",
        "Origami"
    ]
    
    for context in japanese_context:
        test_candidate(context, f"Contexte japonais: {context}")
        
        # Combinaisons avec dates
        test_candidate(f"{context}20130522", f"Contexte + date Nikkei")
        test_candidate(f"{context}20130526", f"Contexte + build date")

def analyze_mathematical_constants():
    """Analyse les constantes mathématiques et cryptographiques"""
    print("\n🔢 ANALYSE DES CONSTANTES MATHÉMATIQUES")
    print("=" * 40)
    
    # Constantes mathématiques importantes
    constants = {
        "Pi": "314159265358979323846264338327950288419716939937510",
        "E": "271828182845904523536028747135266249775724709369995",
        "GoldenRatio": "161803398874989484820458683436563811772030917980576",
        "Sqrt2": "141421356237309504880168872420969807856967187537694",
        "Sqrt3": "173205080756887729352744634150587236694280525381038"
    }
    
    for name, value in constants.items():
        # Tester différentes longueurs
        for length in [32, 64]:
            if len(value) >= length:
                candidate = value[:length]
                test_candidate(candidate, f"Constante {name} ({length} chars)")
        
        # Tester comme hash
        hash_result = hashlib.sha256(value.encode()).hexdigest()
        test_candidate(hash_result, f"Hash de {name}")

def analyze_date_combinations():
    """Analyse les combinaisons de dates importantes"""
    print("\n📅 ANALYSE DES COMBINAISONS DE DATES")
    print("=" * 35)
    
    # Dates importantes
    important_dates = [
        "20130522",  # Nikkei reference
        "20130526",  # Build date
        "20131206",  # Dogecoin launch
        "20090103",  # Bitcoin genesis
        "20111007",  # Litecoin launch
    ]
    
    # Combinaisons de dates
    for i, date1 in enumerate(important_dates):
        for j, date2 in enumerate(important_dates):
            if i != j:
                combined = date1 + date2
                test_candidate(combined, f"Dates combinées: {date1} + {date2}")
                
                # XOR des dates
                try:
                    xor_result = str(int(date1) ^ int(date2))
                    test_candidate(xor_result, f"XOR dates: {date1} ^ {date2}")
                except:
                    pass

def analyze_build_timestamp_variations():
    """Analyse les variations du timestamp de build"""
    print("\n⏰ ANALYSE DES VARIATIONS DE TIMESTAMP")
    print("=" * 38)
    
    # Timestamp de build: 26 mai 2013, 22:13:27 -0700
    build_timestamp = 1369630407  # Unix timestamp
    
    variations = [
        str(build_timestamp),
        hex(build_timestamp)[2:],
        str(build_timestamp)[::-1],  # Inversé
        hex(build_timestamp)[2:][::-1],  # Hex inversé
        
        # Variations mathématiques
        str(build_timestamp * 2),
        str(build_timestamp + 88),  # +88 pour les 88 coins
        str(build_timestamp - 88),
        str(build_timestamp ** 2)[:16],  # Carré tronqué
    ]
    
    for variation in variations:
        test_candidate(variation, f"Variation timestamp: {variation}")

def main():
    print("🏴‍☠️ ANALYSE DU CONTEXTE HISTORIQUE")
    print("=" * 50)
    
    # 1. Analyser le contexte Nintendo
    analyze_nintendo_context()
    
    # 2. Analyser le contexte crypto
    analyze_crypto_context_2013()
    
    # 3. Analyser le contexte japonais
    analyze_japanese_context()
    
    # 4. Analyser les constantes mathématiques
    analyze_mathematical_constants()
    
    # 5. Analyser les combinaisons de dates
    analyze_date_combinations()
    
    # 6. Analyser les variations de timestamp
    analyze_build_timestamp_variations()
    
    print("\n✅ Analyse du contexte historique terminée!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
🏴‍☠️ TREASURE HUNT - ANALYSE CROISÉE DES CODEBASES
Recherche de différences subtiles entre Bellscoin et Dogecoin qui pourraient cacher la clé
"""

import os
import hashlib
import difflib
import re
from pathlib import Path

# Notre clé publique cible
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_hex_as_key(hex_key):
    """Teste une clé hex avec secp256k1"""
    try:
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        
        # Convertir en bytes
        private_key_bytes = bytes.fromhex(hex_key)
        
        # Vérifier que c'est dans la plage valide pour secp256k1
        private_key_int = int(hex_key, 16)
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Générer la clé publique
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Format non compressé (04 + x + y)
        public_key_hex = "04" + vk.to_string().hex()
        
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉🎉🎉 CLÉ PRIVÉE TROUVÉE! 🎉🎉🎉")
            print(f"Clé privée: {hex_key}")
            print(f"Clé publique: {public_key_hex}")
            return True
            
    except Exception as e:
        pass
    
    return False

def compare_files(file1, file2):
    """Compare deux fichiers et retourne les différences"""
    try:
        with open(file1, 'r', encoding='utf-8', errors='ignore') as f1:
            content1 = f1.readlines()
        with open(file2, 'r', encoding='utf-8', errors='ignore') as f2:
            content2 = f2.readlines()
        
        # Générer les différences
        diff = list(difflib.unified_diff(content1, content2, fromfile=file1, tofile=file2, lineterm=''))
        return diff
    except Exception as e:
        return []

def analyze_differences():
    """Analyse les différences entre les fichiers correspondants"""
    print("🔍 ANALYSE DES DIFFÉRENCES ENTRE CODEBASES")
    print("=" * 50)
    
    # Fichiers importants à comparer
    important_files = [
        'src/main.cpp',
        'src/main.h',
        'src/checkpoints.cpp',
        'src/checkpoints.h',
        'src/version.cpp',
        'src/version.h',
        'src/init.cpp',
        'src/util.cpp'
    ]
    
    differences_found = []
    
    for file_path in important_files:
        bellscoin_file = f"bellscoin/{file_path}"
        dogecoin_file = f"dogecoin/{file_path}"
        
        if os.path.exists(bellscoin_file) and os.path.exists(dogecoin_file):
            print(f"\n📄 Comparaison: {file_path}")
            
            diff = compare_files(bellscoin_file, dogecoin_file)
            
            if diff:
                print(f"   ✅ Différences trouvées ({len(diff)} lignes)")
                
                # Analyser les différences pour des patterns suspects
                for line in diff:
                    if line.startswith('+') or line.startswith('-'):
                        # Extraire le contenu de la ligne
                        content = line[1:].strip()
                        
                        # Chercher des hex patterns
                        hex_matches = re.findall(r'[0-9a-fA-F]{32,}', content)
                        for match in hex_matches:
                            if len(match) == 64:
                                print(f"   🔑 Hex dans diff: {match}")
                                differences_found.append(match)
                                test_hex_as_key(match)
                        
                        # Chercher des nombres suspects
                        number_matches = re.findall(r'\b\d{8,}\b', content)
                        for match in number_matches:
                            print(f"   🔢 Nombre dans diff: {match}")
                            differences_found.append(match)
                            # Tester comme hash
                            hash_result = hashlib.sha256(match.encode()).hexdigest()
                            test_hex_as_key(hash_result)
            else:
                print(f"   ❌ Aucune différence")
    
    return differences_found

def analyze_unique_content():
    """Analyse le contenu unique à chaque projet"""
    print("\n🎯 ANALYSE DU CONTENU UNIQUE")
    print("=" * 30)
    
    # Chercher des fichiers uniques
    bellscoin_files = set()
    dogecoin_files = set()
    
    for root, dirs, files in os.walk('bellscoin'):
        for file in files:
            rel_path = os.path.relpath(os.path.join(root, file), 'bellscoin')
            bellscoin_files.add(rel_path)
    
    for root, dirs, files in os.walk('dogecoin'):
        for file in files:
            rel_path = os.path.relpath(os.path.join(root, file), 'dogecoin')
            dogecoin_files.add(rel_path)
    
    # Fichiers uniques à Bellscoin
    unique_to_bellscoin = bellscoin_files - dogecoin_files
    print(f"\n📁 Fichiers uniques à Bellscoin ({len(unique_to_bellscoin)}):")
    for file in sorted(unique_to_bellscoin)[:10]:
        print(f"   - {file}")
        analyze_unique_file(f"bellscoin/{file}")
    
    # Fichiers uniques à Dogecoin
    unique_to_dogecoin = dogecoin_files - bellscoin_files
    print(f"\n📁 Fichiers uniques à Dogecoin ({len(unique_to_dogecoin)}):")
    for file in sorted(unique_to_dogecoin)[:10]:
        print(f"   - {file}")
        analyze_unique_file(f"dogecoin/{file}")

def analyze_unique_file(filepath):
    """Analyse un fichier unique pour des patterns suspects"""
    try:
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Chercher des patterns hex
            hex_matches = re.findall(r'[0-9a-fA-F]{32,}', content)
            for match in hex_matches:
                if len(match) == 64:
                    print(f"     🔑 Hex trouvé: {match}")
                    test_hex_as_key(match)
    except Exception as e:
        pass

def analyze_version_differences():
    """Analyse spécifique des différences de version"""
    print("\n🏷️ ANALYSE DES VERSIONS")
    print("=" * 25)
    
    # Analyser les fichiers de version
    version_files = [
        ('bellscoin/src/version.cpp', 'dogecoin/src/version.cpp'),
        ('bellscoin/src/version.h', 'dogecoin/src/version.h')
    ]
    
    for bell_file, doge_file in version_files:
        if os.path.exists(bell_file) and os.path.exists(doge_file):
            print(f"\n📄 Comparaison versions: {os.path.basename(bell_file)}")
            
            try:
                with open(bell_file, 'r') as f:
                    bell_content = f.read()
                with open(doge_file, 'r') as f:
                    doge_content = f.read()
                
                # Extraire les numéros de version
                bell_versions = re.findall(r'(\d+)\.(\d+)\.(\d+)', bell_content)
                doge_versions = re.findall(r'(\d+)\.(\d+)\.(\d+)', doge_content)
                
                print(f"   Bellscoin versions: {bell_versions}")
                print(f"   Dogecoin versions: {doge_versions}")
                
                # Tester les combinaisons de versions comme clés
                for major, minor, patch in bell_versions + doge_versions:
                    version_str = f"{major}{minor}{patch}"
                    print(f"   🔢 Test version: {version_str}")
                    
                    # Tester directement
                    if len(version_str) >= 6:
                        padded = version_str.ljust(64, '0')
                        test_hex_as_key(padded)
                    
                    # Tester comme hash
                    hash_result = hashlib.sha256(version_str.encode()).hexdigest()
                    test_hex_as_key(hash_result)
                    
            except Exception as e:
                print(f"   ❌ Erreur: {e}")

def analyze_build_differences():
    """Analyse les différences dans les systèmes de build"""
    print("\n🔨 ANALYSE DES BUILDS")
    print("=" * 20)
    
    build_files = [
        'bells-qt.pro',
        'dogecoin-qt.pro',
        'src/makefile.unix',
        'src/makefile.mingw'
    ]
    
    for build_file in build_files:
        bell_path = f"bellscoin/{build_file}"
        doge_path = f"dogecoin/{build_file}"
        
        if os.path.exists(bell_path):
            print(f"\n📄 Analyse build: {build_file} (Bellscoin)")
            analyze_build_file(bell_path)
        
        if os.path.exists(doge_path):
            print(f"\n📄 Analyse build: {build_file} (Dogecoin)")
            analyze_build_file(doge_path)

def analyze_build_file(filepath):
    """Analyse un fichier de build pour des patterns suspects"""
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Chercher des définitions de constantes
        defines = re.findall(r'#define\s+\w+\s+([0-9a-fA-F]+)', content)
        for define in defines:
            if len(define) >= 8:
                print(f"   🔧 Define trouvé: {define}")
                if len(define) == 64:
                    test_hex_as_key(define)
                else:
                    # Tester comme hash
                    hash_result = hashlib.sha256(define.encode()).hexdigest()
                    test_hex_as_key(hash_result)
        
        # Chercher des variables de build
        variables = re.findall(r'(\w+)\s*=\s*([0-9a-fA-F]{8,})', content)
        for var_name, var_value in variables:
            print(f"   🔧 Variable {var_name}: {var_value}")
            if len(var_value) == 64:
                test_hex_as_key(var_value)
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

def main():
    print("🏴‍☠️ ANALYSE CROISÉE DES CODEBASES")
    print("=" * 50)
    
    # 1. Analyser les différences entre fichiers
    differences = analyze_differences()
    
    # 2. Analyser le contenu unique
    analyze_unique_content()
    
    # 3. Analyser les versions
    analyze_version_differences()
    
    # 4. Analyser les builds
    analyze_build_differences()
    
    print(f"\n✅ Analyse croisée terminée!")
    print(f"📊 {len(differences)} différences analysées")

if __name__ == "__main__":
    main()

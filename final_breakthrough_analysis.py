#!/usr/bin/env python3
"""
🏴‍☠️ TREASURE HUNT - ANALYSE FINALE POUR LA PERCÉE
Focus sur les découvertes les plus prometteuses
"""

import hashlib
import datetime
import struct

# Notre clé publique cible
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_hex_as_key(hex_key):
    """Teste une clé hex avec secp256k1"""
    try:
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        
        # Convertir en bytes
        private_key_bytes = bytes.fromhex(hex_key)
        
        # Vérifier que c'est dans la plage valide pour secp256k1
        private_key_int = int(hex_key, 16)
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Générer la clé publique
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Format non compressé (04 + x + y)
        public_key_hex = "04" + vk.to_string().hex()
        
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉🎉🎉 CLÉ PRIVÉE TROUVÉE! 🎉🎉🎉")
            print(f"Clé privée: {hex_key}")
            print(f"Clé publique: {public_key_hex}")
            return True
            
    except Exception as e:
        pass
    
    return False

def test_candidate(candidate, description):
    """Teste un candidat comme clé privée"""
    print(f"🔍 Test: {description}")
    
    # Nettoyer le candidat
    clean_candidate = str(candidate).strip().lower()
    
    # Tester directement si c'est un hex de 64 caractères
    if len(clean_candidate) == 64 and all(c in '0123456789abcdef' for c in clean_candidate):
        if test_hex_as_key(clean_candidate):
            return True
    
    # Tester comme hash SHA256
    hash_result = hashlib.sha256(str(candidate).encode()).hexdigest()
    if test_hex_as_key(hash_result):
        return True
    
    return False

def analyze_mystery_hash():
    """Analyse approfondie du hash mystérieux e5be24df..."""
    print("🔍 ANALYSE DU HASH MYSTÉRIEUX")
    print("=" * 35)
    
    mystery_hash = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
    print(f"Hash mystérieux: {mystery_hash}")
    
    # 1. Tester directement
    test_candidate(mystery_hash, "Hash mystérieux direct")
    
    # 2. Tester des variations
    test_candidate(mystery_hash[:32], "32 premiers caractères")
    test_candidate(mystery_hash[32:], "32 derniers caractères")
    test_candidate(mystery_hash[::-1], "Hash inversé")
    
    # 3. Analyser comme timestamp potentiel
    # Diviser en chunks de 8 caractères (32-bit values)
    chunks = [mystery_hash[i:i+8] for i in range(0, len(mystery_hash), 8)]
    for i, chunk in enumerate(chunks):
        try:
            # Interpréter comme timestamp Unix
            timestamp = int(chunk, 16)
            if 1000000000 <= timestamp <= 2000000000:  # Plage raisonnable
                date = datetime.datetime.fromtimestamp(timestamp)
                print(f"   📅 Chunk {i+1} ({chunk}) = {timestamp} = {date}")
                test_candidate(str(timestamp), f"Timestamp du chunk {i+1}")
        except:
            pass
    
    # 4. XOR avec des valeurs connues
    known_values = [
        "4e696e746f6e646f",  # "Nintondo" en hex
        "1380206665",        # Timestamp fréquent
        "1383363386"         # Autre timestamp
    ]
    
    for value in known_values:
        try:
            # Padding si nécessaire
            padded_value = value.ljust(64, '0')
            mystery_int = int(mystery_hash, 16)
            value_int = int(padded_value, 16)
            xor_result = mystery_int ^ value_int
            xor_hex = format(xor_result, '064x')
            test_candidate(xor_hex, f"Hash XOR avec {value}")
        except:
            pass

def analyze_uuid_timestamps():
    """Analyse approfondie des timestamps dans les UUIDs"""
    print("\n⏰ ANALYSE DES TIMESTAMPS UUID")
    print("=" * 30)
    
    # UUIDs trouvés dans les images
    uuids = [
        "F7BF36A8624311E38BDFBAF467890C9E",
        "F7465D7C5CB311E3A2A9B43E913BA9F6"
    ]
    
    for uuid in uuids:
        print(f"\n🆔 Analyse UUID: {uuid}")
        
        # Extraire le timestamp UUID v1
        # Format UUID v1: time_low-time_mid-time_hi_and_version-clock_seq-node
        time_low = uuid[:8]
        time_mid = uuid[8:12]
        time_hi_and_version = uuid[12:16]
        
        # Reconstruire le timestamp (approximation)
        time_hi = time_hi_and_version[1:]  # Enlever le bit de version
        full_timestamp = time_hi + time_mid + time_low
        
        print(f"   ⏰ Timestamp reconstruit: {full_timestamp}")
        test_candidate(full_timestamp, f"Timestamp UUID: {full_timestamp}")
        
        # Convertir en timestamp Unix (approximation)
        try:
            # UUID timestamp est en 100-nanosecond intervals depuis 1582-10-15
            uuid_timestamp = int(full_timestamp, 16)
            # Conversion approximative vers Unix timestamp
            unix_timestamp = (uuid_timestamp - 0x01B21DD213814000) // 10000000
            if 1000000000 <= unix_timestamp <= 2000000000:
                date = datetime.datetime.fromtimestamp(unix_timestamp)
                print(f"   📅 Date approximative: {date}")
                test_candidate(str(unix_timestamp), f"Unix timestamp de UUID: {unix_timestamp}")
        except:
            pass

def analyze_image_metadata_patterns():
    """Analyse les patterns dans les métadonnées d'images"""
    print("\n🖼️ ANALYSE DES PATTERNS D'IMAGES")
    print("=" * 33)
    
    # Timestamps fréquents trouvés dans les images
    frequent_timestamps = [
        1380206665,  # Le plus fréquent
        1383363386,  # Dans splash images
        1386889792,  # Dans about.png
    ]
    
    # Analyser les relations entre ces timestamps
    for i, ts1 in enumerate(frequent_timestamps):
        for j, ts2 in enumerate(frequent_timestamps):
            if i != j:
                # Différence
                diff = abs(ts1 - ts2)
                print(f"📊 Différence {ts1} - {ts2} = {diff}")
                test_candidate(str(diff), f"Différence timestamps: {diff}")
                
                # Moyenne
                avg = (ts1 + ts2) // 2
                test_candidate(str(avg), f"Moyenne timestamps: {avg}")
                
                # Produit modulo
                product = (ts1 * ts2) % (2**64)
                product_hex = format(product, 'x')
                if len(product_hex) <= 64:
                    test_candidate(product_hex, f"Produit timestamps: {product_hex}")

def analyze_build_date_correlation():
    """Analyse la corrélation avec la date de build"""
    print("\n🔨 ANALYSE DE LA DATE DE BUILD")
    print("=" * 30)
    
    # Date de build: 26 mai 2013, 22:13:27 -0700
    build_timestamp = 1369630407
    build_date = datetime.datetime.fromtimestamp(build_timestamp)
    print(f"📅 Date de build: {build_date}")
    
    # Analyser les composants de la date
    year = build_date.year      # 2013
    month = build_date.month    # 5
    day = build_date.day        # 26
    hour = build_date.hour      # 22 (ou différent selon timezone)
    minute = build_date.minute  # 13
    second = build_date.second  # 27
    
    # Combinaisons significatives
    combinations = [
        f"{year}{month:02d}{day:02d}",           # 20130526
        f"{year}{month:02d}{day:02d}{hour:02d}", # 2013052622
        f"{day:02d}{month:02d}{year}",           # 26052013
        f"{hour:02d}{minute:02d}{second:02d}",   # 221327
        f"{year}{day:02d}{month:02d}",           # 20132605
    ]
    
    for combo in combinations:
        test_candidate(combo, f"Combinaison date: {combo}")
        
        # Avec "Nintondo"
        nintondo_hex = "4e696e746f6e646f"
        combined = combo + nintondo_hex
        if len(combined) <= 64:
            test_candidate(combined, f"Date + Nintondo: {combined}")

def analyze_nintendo_easter_eggs():
    """Recherche d'easter eggs Nintendo spécifiques"""
    print("\n🎮 ANALYSE DES EASTER EGGS NINTENDO")
    print("=" * 35)
    
    # Codes produits Nintendo spécifiques à 2013
    nintendo_codes = [
        "CTR001",    # Nintendo 3DS
        "WUP001",    # Wii U GamePad
        "WUP010",    # Wii U Console
        "KTR001",    # New Nintendo 3DS (développement en 2013)
    ]
    
    for code in nintendo_codes:
        test_candidate(code, f"Code produit Nintendo: {code}")
        
        # Avec années
        test_candidate(f"{code}2013", f"Code + 2013: {code}2013")
        test_candidate(f"2013{code}", f"2013 + Code: 2013{code}")
        
        # En hex
        hex_code = code.encode().hex()
        test_candidate(hex_code, f"Code en hex: {hex_code}")

def analyze_final_combinations():
    """Analyse finale des combinaisons les plus prometteuses"""
    print("\n🎯 ANALYSE FINALE DES COMBINAISONS")
    print("=" * 35)
    
    # Éléments clés identifiés
    key_elements = {
        "nintondo": "4e696e746f6e646f",
        "build_date": "20130526",
        "nikkei_date": "20130522", 
        "mystery_hash_start": "e5be24df",
        "uuid_pattern": "624311E3",
        "frequent_timestamp": "1380206665"
    }
    
    # Tester toutes les combinaisons de 2 éléments
    elements = list(key_elements.items())
    for i, (name1, value1) in enumerate(elements):
        for j, (name2, value2) in enumerate(elements):
            if i != j:
                combined = value1 + value2
                if len(combined) <= 64:
                    test_candidate(combined, f"{name1} + {name2}: {combined}")
                
                # XOR
                try:
                    if len(value1) == len(value2):
                        int1 = int(value1, 16) if all(c in '0123456789abcdef' for c in value1.lower()) else int(value1)
                        int2 = int(value2, 16) if all(c in '0123456789abcdef' for c in value2.lower()) else int(value2)
                        xor_result = int1 ^ int2
                        xor_hex = format(xor_result, 'x')
                        if len(xor_hex) <= 64:
                            test_candidate(xor_hex, f"{name1} XOR {name2}: {xor_hex}")
                except:
                    pass

def main():
    print("🏴‍☠️ ANALYSE FINALE POUR LA PERCÉE")
    print("=" * 50)
    
    # 1. Analyser le hash mystérieux
    analyze_mystery_hash()
    
    # 2. Analyser les timestamps UUID
    analyze_uuid_timestamps()
    
    # 3. Analyser les patterns d'images
    analyze_image_metadata_patterns()
    
    # 4. Analyser la corrélation avec la date de build
    analyze_build_date_correlation()
    
    # 5. Rechercher des easter eggs Nintendo
    analyze_nintendo_easter_eggs()
    
    # 6. Analyse finale des combinaisons
    analyze_final_combinations()
    
    print("\n✅ Analyse finale terminée!")
    print("🔍 Si aucune clé n'a été trouvée, la solution pourrait nécessiter:")
    print("   - Une information externe non présente dans le code")
    print("   - Un algorithme de dérivation plus complexe")
    print("   - Une combinaison que nous n'avons pas encore testée")

if __name__ == "__main__":
    main()

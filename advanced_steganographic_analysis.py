#!/usr/bin/env python3
"""
🏴‍☠️ TREASURE HUNT - ANALYSE STÉGANOGRAPHIQUE AVANCÉE
Recherche de clés privées cachées dans les métadonnées et fichiers binaires
"""

import os
import hashlib
import binascii
import struct
from pathlib import Path
import re

# Notre clé publique cible
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def analyze_binary_file(filepath):
    """Analyse un fichier binaire pour chercher des patterns cachés"""
    print(f"\n🔍 Analyse de {filepath}")
    
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
        
        # 1. Chercher des strings ASCII cachées
        ascii_strings = re.findall(b'[A-Za-z0-9]{20,}', data)
        for s in ascii_strings[:10]:  # Limiter l'affichage
            try:
                decoded = s.decode('ascii')
                if len(decoded) >= 32:  # Potentielle clé privée
                    print(f"   📝 String longue: {decoded[:50]}...")
                    test_as_private_key(decoded)
            except:
                pass
        
        # 2. Chercher des patterns hex
        hex_patterns = re.findall(b'[0-9a-fA-F]{32,}', data)
        for pattern in hex_patterns[:10]:
            try:
                hex_str = pattern.decode('ascii')
                if len(hex_str) == 64:  # Longueur d'une clé privée
                    print(f"   🔑 Pattern hex 64 chars: {hex_str}")
                    test_as_private_key(hex_str)
            except:
                pass
        
        # 3. Analyser les métadonnées PE (Windows executables)
        if filepath.endswith('.exe') or filepath.endswith('.dll'):
            analyze_pe_metadata(data)
        
        # 4. Chercher des timestamps cachés
        analyze_timestamps(data)
        
        # 5. Analyser les checksums et hashes
        analyze_checksums(data, filepath)
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

def analyze_pe_metadata(data):
    """Analyse les métadonnées PE d'un exécutable Windows"""
    try:
        # Chercher la signature PE
        pe_offset = struct.unpack('<I', data[0x3c:0x40])[0]
        if data[pe_offset:pe_offset+4] == b'PE\x00\x00':
            print("   📋 Fichier PE détecté")
            
            # Timestamp de compilation
            timestamp = struct.unpack('<I', data[pe_offset+8:pe_offset+12])[0]
            print(f"   ⏰ Timestamp PE: {timestamp} ({hex(timestamp)})")
            test_as_private_key(str(timestamp))
            test_as_private_key(hex(timestamp)[2:])
            
    except Exception as e:
        print(f"   ❌ Erreur PE: {e}")

def analyze_timestamps(data):
    """Cherche des timestamps cachés dans les données"""
    # Chercher des patterns de timestamp Unix
    for i in range(0, len(data) - 4, 4):
        try:
            timestamp = struct.unpack('<I', data[i:i+4])[0]
            # Timestamps autour de 2013 (1356998400 = 1 Jan 2013, 1388534400 = 1 Jan 2014)
            if 1356998400 <= timestamp <= 1388534400:
                print(f"   📅 Timestamp 2013 trouvé: {timestamp} à offset {i}")
                test_as_private_key(str(timestamp))
                test_as_private_key(hex(timestamp)[2:])
        except:
            continue

def analyze_checksums(data, filepath):
    """Analyse les checksums et hashes du fichier"""
    # Hash du fichier complet
    file_hash = hashlib.sha256(data).hexdigest()
    print(f"   🔐 SHA256: {file_hash}")
    test_as_private_key(file_hash)
    
    # Hash de sections spécifiques
    if len(data) > 1024:
        section_hash = hashlib.sha256(data[:1024]).hexdigest()
        print(f"   🔐 SHA256 (1024 premiers bytes): {section_hash}")
        test_as_private_key(section_hash)

def test_as_private_key(candidate):
    """Teste si un candidat peut être une clé privée"""
    try:
        # Nettoyer le candidat
        clean_candidate = candidate.strip()
        
        # Tester directement
        if len(clean_candidate) == 64 and all(c in '0123456789abcdefABCDEF' for c in clean_candidate):
            # C'est un hex valide de 64 caractères
            test_hex_as_key(clean_candidate.lower())
        
        # Tester comme hash
        if len(clean_candidate) > 10:
            hash_result = hashlib.sha256(clean_candidate.encode()).hexdigest()
            test_hex_as_key(hash_result)
            
    except Exception as e:
        pass

def test_hex_as_key(hex_key):
    """Teste une clé hex avec secp256k1"""
    try:
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        
        # Convertir en bytes
        private_key_bytes = bytes.fromhex(hex_key)
        
        # Vérifier que c'est dans la plage valide pour secp256k1
        private_key_int = int(hex_key, 16)
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return
        
        # Générer la clé publique
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Format non compressé (04 + x + y)
        public_key_hex = "04" + vk.to_string().hex()
        
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉🎉🎉 CLÉ PRIVÉE TROUVÉE! 🎉🎉🎉")
            print(f"Clé privée: {hex_key}")
            print(f"Clé publique: {public_key_hex}")
            return True
            
    except Exception as e:
        pass
    
    return False

def analyze_image_files():
    """Analyse les fichiers images pour la stéganographie"""
    print("\n🖼️ ANALYSE DES IMAGES")
    
    image_extensions = ['.bmp', '.ico', '.xpm', '.png', '.jpg']
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if any(file.lower().endswith(ext) for ext in image_extensions):
                filepath = os.path.join(root, file)
                analyze_binary_file(filepath)

def analyze_config_files():
    """Analyse les fichiers de configuration"""
    print("\n⚙️ ANALYSE DES FICHIERS DE CONFIGURATION")
    
    config_files = [
        'dogecoin/release/dogecoin.conf',
        'bellscoin/share/setup.nsi',
        'dogecoin/share/setup.nsi'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"\n📄 Analyse de {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                # Chercher des patterns hex
                hex_matches = re.findall(r'[0-9a-fA-F]{32,}', content)
                for match in hex_matches:
                    if len(match) == 64:
                        print(f"   🔑 Hex trouvé: {match}")
                        test_as_private_key(match)
                        
            except Exception as e:
                print(f"   ❌ Erreur: {e}")

def main():
    print("🏴‍☠️ ANALYSE STÉGANOGRAPHIQUE AVANCÉE")
    print("=" * 50)
    
    # 1. Analyser tous les fichiers binaires
    print("\n🔍 ANALYSE DES FICHIERS BINAIRES")
    binary_extensions = ['.dll', '.exe']
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if any(file.lower().endswith(ext) for ext in binary_extensions):
                filepath = os.path.join(root, file)
                analyze_binary_file(filepath)
    
    # 2. Analyser les images
    analyze_image_files()
    
    # 3. Analyser les fichiers de configuration
    analyze_config_files()
    
    print("\n✅ Analyse stéganographique terminée!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
🏴‍☠️ TREASURE HUNT - ANALYSE DES PATTERNS SUSPECTS
Analyse approfondie des patterns trouvés dans l'analyse stéganographique
"""

import hashlib
import re

# Notre clé publique cible
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_hex_as_key(hex_key):
    """Teste une clé hex avec secp256k1"""
    try:
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        
        # Convertir en bytes
        private_key_bytes = bytes.fromhex(hex_key)
        
        # Vérifier que c'est dans la plage valide pour secp256k1
        private_key_int = int(hex_key, 16)
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Générer la clé publique
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Format non compressé (04 + x + y)
        public_key_hex = "04" + vk.to_string().hex()
        
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉🎉🎉 CLÉ PRIVÉE TROUVÉE! 🎉🎉🎉")
            print(f"Clé privée: {hex_key}")
            print(f"Clé publique: {public_key_hex}")
            return True
            
    except Exception as e:
        pass
    
    return False

def test_candidate(candidate, description):
    """Teste un candidat comme clé privée"""
    print(f"🔍 Test: {description}")
    
    # Nettoyer le candidat
    clean_candidate = candidate.strip().lower()
    
    # Tester directement si c'est un hex de 64 caractères
    if len(clean_candidate) == 64 and all(c in '0123456789abcdef' for c in clean_candidate):
        if test_hex_as_key(clean_candidate):
            return True
    
    # Tester comme hash SHA256
    hash_result = hashlib.sha256(candidate.encode()).hexdigest()
    if test_hex_as_key(hash_result):
        return True
    
    return False

def analyze_suspicious_strings():
    """Analyse les strings suspectes trouvées dans les images"""
    print("🔍 ANALYSE DES STRINGS SUSPECTES")
    print("=" * 40)
    
    # Strings trouvées dans l'analyse stéganographique
    suspicious_strings = [
        "F7BF36A8624311E38BDFBAF467890C9E",
        "F7BF36A9624311E38BDFBAF467890C9E", 
        "F7BF36A6624311E38BDFBAF467890C9E",
        "F7BF36A7624311E38BDFBAF467890C9E",
        "F7465D7C5CB311E3A2A9B43E913BA9F6",
        "F7465D7D5CB311E3A2A9B43E913BA9F6",
        "F7465D7A5CB311E3A2A9B43E913BA9F6",
        "F7465D7B5CB311E3A2A9B43E913BA9F6"
    ]
    
    for string in suspicious_strings:
        print(f"\n📝 Analyse de: {string}")
        
        # Tester directement
        test_candidate(string, f"String directe: {string}")
        
        # Analyser les patterns dans la string
        # Ces strings semblent avoir un pattern: F7xxxxx624311E3 ou F7xxxxx5CB311E3
        if "624311E3" in string:
            print(f"   🎯 Pattern '624311E3' détecté")
            # Extraire les parties avant et après
            parts = string.split("624311E3")
            if len(parts) == 2:
                before = parts[0]  # F7BF36A8
                after = parts[1]   # 8BDFBAF467890C9E
                print(f"   📊 Avant: {before}, Après: {after}")
                
                # Tester les parties séparément
                test_candidate(before, f"Partie avant 624311E3: {before}")
                test_candidate(after, f"Partie après 624311E3: {after}")
                
                # Tester des combinaisons
                test_candidate(before + after, f"Combinaison: {before + after}")
        
        if "5CB311E3" in string:
            print(f"   🎯 Pattern '5CB311E3' détecté")
            parts = string.split("5CB311E3")
            if len(parts) == 2:
                before = parts[0]
                after = parts[1]
                print(f"   📊 Avant: {before}, Après: {after}")
                
                test_candidate(before, f"Partie avant 5CB311E3: {before}")
                test_candidate(after, f"Partie après 5CB311E3: {after}")
                test_candidate(before + after, f"Combinaison: {before + after}")

def analyze_timestamps():
    """Analyse les timestamps suspects trouvés"""
    print("\n⏰ ANALYSE DES TIMESTAMPS SUSPECTS")
    print("=" * 35)
    
    # Timestamps intéressants trouvés dans l'analyse
    timestamps = [
        1380206665,  # Très fréquent dans les PNG
        1383363386,  # Trouvé dans splash2.jpg et splash2.png
        1386889792,  # Trouvé dans about.png
        1361969025,  # Trouvé dans bitcoin.ico et favicon.ico
        1378746220,  # Trouvé dans bitcoin.ico et favicon.ico
        1381286547,  # Trouvé dans bitcoin.png
        1381802000,  # Trouvé dans bitcoin.png
    ]
    
    for timestamp in timestamps:
        print(f"\n📅 Analyse timestamp: {timestamp}")
        
        # Tester directement
        test_candidate(str(timestamp), f"Timestamp: {timestamp}")
        test_candidate(hex(timestamp)[2:], f"Timestamp hex: {hex(timestamp)[2:]}")
        
        # Tester des variations
        test_candidate(str(timestamp)[::-1], f"Timestamp inversé: {str(timestamp)[::-1]}")
        
        # Combinaisons avec d'autres timestamps
        for other_ts in [1380206665, 1383363386]:  # Les plus fréquents
            if timestamp != other_ts:
                combined = str(timestamp) + str(other_ts)
                if len(combined) <= 64:
                    test_candidate(combined, f"Timestamps combinés: {timestamp} + {other_ts}")

def analyze_pattern_combinations():
    """Analyse les combinaisons de patterns"""
    print("\n🔗 ANALYSE DES COMBINAISONS DE PATTERNS")
    print("=" * 42)
    
    # Patterns récurrents
    patterns = {
        "624311E3": "Pattern fréquent dans les strings",
        "5CB311E3": "Pattern alternatif dans les strings", 
        "F7BF36A": "Préfixe fréquent",
        "F7465D7": "Préfixe alternatif",
        "1380206665": "Timestamp le plus fréquent",
        "1383363386": "Timestamp dans splash images"
    }
    
    pattern_list = list(patterns.keys())
    
    # Tester toutes les combinaisons de 2 patterns
    for i, pattern1 in enumerate(pattern_list):
        for j, pattern2 in enumerate(pattern_list):
            if i != j:
                combined = pattern1 + pattern2
                if len(combined) <= 64:
                    test_candidate(combined, f"Patterns combinés: {pattern1} + {pattern2}")

def analyze_hex_arithmetic():
    """Analyse arithmétique sur les patterns hex"""
    print("\n🧮 ANALYSE ARITHMÉTIQUE DES PATTERNS")
    print("=" * 38)
    
    # Convertir les patterns en nombres pour des opérations
    hex_patterns = [
        "F7BF36A8",
        "624311E3", 
        "8BDFBAF4",
        "67890C9E",
        "F7465D7C",
        "5CB311E3",
        "A2A9B43E",
        "913BA9F6"
    ]
    
    for i, pattern1 in enumerate(hex_patterns):
        for j, pattern2 in enumerate(hex_patterns):
            if i != j:
                try:
                    num1 = int(pattern1, 16)
                    num2 = int(pattern2, 16)
                    
                    # XOR
                    xor_result = num1 ^ num2
                    xor_hex = format(xor_result, 'x')
                    if len(xor_hex) <= 64:
                        test_candidate(xor_hex, f"XOR: {pattern1} ^ {pattern2}")
                    
                    # Addition (modulo pour éviter les débordements)
                    add_result = (num1 + num2) % (2**256)
                    add_hex = format(add_result, 'x')
                    if len(add_hex) <= 64:
                        test_candidate(add_hex, f"ADD: {pattern1} + {pattern2}")
                        
                except Exception as e:
                    continue

def analyze_uuid_patterns():
    """Analyse les patterns qui ressemblent à des UUIDs"""
    print("\n🆔 ANALYSE DES PATTERNS UUID")
    print("=" * 28)
    
    # Les strings trouvées ressemblent à des UUIDs
    # Format: F7BF36A8-6243-11E3-8BDF-BAF467890C9E
    uuid_like_strings = [
        "F7BF36A8624311E38BDFBAF467890C9E",
        "F7465D7C5CB311E3A2A9B43E913BA9F6"
    ]
    
    for uuid_str in uuid_like_strings:
        print(f"\n🆔 Analyse UUID-like: {uuid_str}")
        
        # Essayer de reconstruire le format UUID standard
        if len(uuid_str) == 32:
            # Format: 8-4-4-4-12
            formatted_uuid = f"{uuid_str[:8]}-{uuid_str[8:12]}-{uuid_str[12:16]}-{uuid_str[16:20]}-{uuid_str[20:]}"
            print(f"   📋 UUID formaté: {formatted_uuid}")
            
            # Tester chaque partie
            parts = [uuid_str[:8], uuid_str[8:12], uuid_str[12:16], uuid_str[16:20], uuid_str[20:]]
            for i, part in enumerate(parts):
                test_candidate(part, f"Partie UUID {i+1}: {part}")
            
            # Analyser le timestamp dans l'UUID (si c'est un UUID v1)
            # Les UUIDs v1 contiennent un timestamp
            if uuid_str[12] == '1':  # Version 1 UUID
                print(f"   ⏰ UUID Version 1 détecté (contient timestamp)")
                # Extraire le timestamp (format complexe, approximation)
                time_part = uuid_str[13:16] + uuid_str[8:12] + uuid_str[:8]
                test_candidate(time_part, f"Timestamp UUID: {time_part}")

def main():
    print("🏴‍☠️ ANALYSE DES PATTERNS SUSPECTS")
    print("=" * 50)
    
    # 1. Analyser les strings suspectes
    analyze_suspicious_strings()
    
    # 2. Analyser les timestamps
    analyze_timestamps()
    
    # 3. Analyser les combinaisons
    analyze_pattern_combinations()
    
    # 4. Analyser l'arithmétique hex
    analyze_hex_arithmetic()
    
    # 5. Analyser les patterns UUID
    analyze_uuid_patterns()
    
    print("\n✅ Analyse des patterns suspects terminée!")

if __name__ == "__main__":
    main()

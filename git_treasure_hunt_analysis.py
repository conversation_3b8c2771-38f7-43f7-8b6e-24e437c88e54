#!/usr/bin/env python3
"""
🏴‍☠️ TREASURE HUNT - ANALYSE DE L'HISTORIQUE GIT
Recherche de clés privées dans les commits, hashes, et métadonnées Git
"""

import hashlib
import subprocess
import re
from datetime import datetime

# Notre clé publique cible
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_hex_as_key(hex_key):
    """Teste une clé hex avec secp256k1"""
    try:
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        
        # Convertir en bytes
        private_key_bytes = bytes.fromhex(hex_key)
        
        # Vérifier que c'est dans la plage valide pour secp256k1
        private_key_int = int(hex_key, 16)
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Générer la clé publique
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Format non compressé (04 + x + y)
        public_key_hex = "04" + vk.to_string().hex()
        
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉🎉🎉 CLÉ PRIVÉE TROUVÉE! 🎉🎉🎉")
            print(f"Clé privée: {hex_key}")
            print(f"Clé publique: {public_key_hex}")
            return True
            
    except Exception as e:
        pass
    
    return False

def test_candidate(candidate, description):
    """Teste un candidat comme clé privée"""
    print(f"🔍 Test: {description}")
    
    # Nettoyer le candidat
    clean_candidate = str(candidate).strip().lower()
    
    # Tester directement si c'est un hex de 64 caractères
    if len(clean_candidate) == 64 and all(c in '0123456789abcdef' for c in clean_candidate):
        if test_hex_as_key(clean_candidate):
            return True
    
    # Tester comme hash SHA256
    hash_result = hashlib.sha256(str(candidate).encode()).hexdigest()
    if test_hex_as_key(hash_result):
        return True
    
    return False

def analyze_commit_hashes():
    """Analyse les hashes de commits comme clés potentielles"""
    print("🔍 ANALYSE DES HASHES DE COMMITS")
    print("=" * 35)
    
    # Hashes de commits trouvés
    commit_hashes = [
        "d3d61922473c4ded168d75c7c1e1d4a87a30857a",  # readme change and max coin change
        "c8006a7c1fef76f48b7c6f3fd86d9573223bf03c",  # change rewards and max amount
        "fa948a214ac0c0dec32825a885fa0ed54c2a7976",  # fix thread bug on linux
        "8d3dfd4bbd375394d61d87bbe8a7b23d4de5e1c6",  # name collision
        "8e0886759c20d848866521291edaa0f69cbde1e0",  # reward changes
        "409234ea1cc4ae570874802c572fa986ea97dbcb",  # cleanup
        "2af0908c5a12bac881207ae41805e5b2e1893308"   # changes (commit initial)
    ]
    
    for commit_hash in commit_hashes:
        # Tester le hash complet
        test_candidate(commit_hash, f"Hash commit complet: {commit_hash}")
        
        # Tester les 32 premiers caractères (64 hex chars)
        if len(commit_hash) >= 32:
            first_32 = commit_hash[:32]
            test_candidate(first_32, f"32 premiers chars: {first_32}")
        
        # Tester des variations
        test_candidate(commit_hash[::-1], f"Hash inversé: {commit_hash[::-1]}")

def analyze_critical_commit_changes():
    """Analyse les changements critiques du commit initial"""
    print("\n🚨 ANALYSE DU COMMIT CRITIQUE")
    print("=" * 30)
    
    # Changements critiques trouvés dans le commit 2af0908c
    critical_changes = {
        "old_genesis_hash": "c46e3d7f70c0ef730ae2b2963b7c0abf711526bce84f390d4e0be24e9650b557",
        "new_genesis_hash": "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698",
        "old_timestamp": "Nintondo!",
        "new_timestamp": "Nintondo",
        "old_time": 1369197950,
        "new_time": 1383509530,
        "old_nonce": 1197600,
        "new_nonce": 44481,
        "old_merkle": "12f695439878919a6a68a55cf39bc25e0a55984fa89036b69c7458e1d240632e",
        "new_merkle": "5b2a3f53f605d62c53e62932dac6925e3d74afa5a4b459745c36d42d0ed26a69",
        "old_message_start": "fbc0b6db",
        "new_message_start": "c0c0c0c0"
    }
    
    for key, value in critical_changes.items():
        print(f"\n📊 {key}: {value}")
        test_candidate(str(value), f"{key}: {value}")
        
        # Tester des combinaisons
        if "old_" in key:
            new_key = key.replace("old_", "new_")
            if new_key in critical_changes:
                old_val = str(value)
                new_val = str(critical_changes[new_key])
                combined = old_val + new_val
                if len(combined) <= 64:
                    test_candidate(combined, f"Combinaison {key} + {new_key}")

def analyze_author_metadata():
    """Analyse les métadonnées de l'auteur"""
    print("\n👤 ANALYSE DES MÉTADONNÉES AUTEUR")
    print("=" * 32)
    
    author_data = {
        "name": "Billy Markus",
        "email": "<EMAIL>",
        "username": "billym2k",
        "hostname": "billym2k-laptop",
        "domain": "ftrdhcpuser.net"
    }
    
    for key, value in author_data.items():
        test_candidate(value, f"Auteur {key}: {value}")
        
        # Tester en hex
        hex_value = value.encode().hex()
        test_candidate(hex_value, f"Auteur {key} en hex: {hex_value}")

def analyze_commit_timestamps():
    """Analyse les timestamps des commits"""
    print("\n⏰ ANALYSE DES TIMESTAMPS DE COMMITS")
    print("=" * 35)
    
    # Timestamps des commits (en secondes Unix)
    commit_timestamps = [
        1385348640,  # 2013-11-24 20:24:00 -0800 (readme change)
        1385347705,  # 2013-11-24 20:08:25 -0800 (change rewards)
        1385325400,  # 2013-11-24 13:56:40 -0800 (fix thread bug)
        1385312251,  # 2013-11-24 10:17:31 -0800 (name collision)
        1385310759,  # 2013-11-24 09:52:39 -0800 (reward changes)
        1383535970,  # 2013-11-03 20:32:50 -0800 (cleanup)
        1383535347   # 2013-11-03 20:22:27 -0800 (changes - commit initial)
    ]
    
    for timestamp in commit_timestamps:
        test_candidate(str(timestamp), f"Timestamp: {timestamp}")
        test_candidate(hex(timestamp)[2:], f"Timestamp hex: {hex(timestamp)[2:]}")
        
        # Date lisible
        date = datetime.fromtimestamp(timestamp)
        print(f"   📅 {timestamp} = {date}")

def analyze_version_changes():
    """Analyse les changements de version"""
    print("\n🏷️ ANALYSE DES CHANGEMENTS DE VERSION")
    print("=" * 37)
    
    version_changes = {
        "client_version_revision": {"old": 3, "new": 4},
        "protocol_version": {"old": 60001, "new": 60003},
        "noblks_version_start": {"old": 32000, "new": 60000},
        "noblks_version_end": {"old": 32400, "new": 60002}
    }
    
    for change_name, versions in version_changes.items():
        old_val = versions["old"]
        new_val = versions["new"]
        
        test_candidate(str(old_val), f"{change_name} old: {old_val}")
        test_candidate(str(new_val), f"{change_name} new: {new_val}")
        
        # Différence
        diff = abs(new_val - old_val)
        test_candidate(str(diff), f"{change_name} diff: {diff}")

def analyze_message_start_pattern():
    """Analyse le pattern message start"""
    print("\n📡 ANALYSE DU MESSAGE START")
    print("=" * 27)
    
    # Changement de message start: fbc0b6db -> c0c0c0c0
    old_msg = "fbc0b6db"
    new_msg = "c0c0c0c0"
    
    test_candidate(old_msg, f"Ancien message start: {old_msg}")
    test_candidate(new_msg, f"Nouveau message start: {new_msg}")
    
    # Pattern c0c0c0c0 est intéressant - répétition de c0
    test_candidate("c0" * 32, "Pattern c0 répété 32 fois")
    
    # XOR entre ancien et nouveau
    try:
        old_int = int(old_msg, 16)
        new_int = int(new_msg, 16)
        xor_result = old_int ^ new_int
        xor_hex = format(xor_result, 'x')
        test_candidate(xor_hex, f"XOR message start: {xor_hex}")
    except:
        pass

def analyze_nonce_patterns():
    """Analyse les patterns de nonce"""
    print("\n🎯 ANALYSE DES PATTERNS DE NONCE")
    print("=" * 30)
    
    # Changement de nonce: 1197600 -> 44481
    old_nonce = 1197600
    new_nonce = 44481
    
    test_candidate(str(old_nonce), f"Ancien nonce: {old_nonce}")
    test_candidate(str(new_nonce), f"Nouveau nonce: {new_nonce}")
    test_candidate(hex(old_nonce)[2:], f"Ancien nonce hex: {hex(old_nonce)[2:]}")
    test_candidate(hex(new_nonce)[2:], f"Nouveau nonce hex: {hex(new_nonce)[2:]}")
    
    # Différence et opérations
    diff = abs(old_nonce - new_nonce)
    test_candidate(str(diff), f"Différence nonces: {diff}")
    
    # Combinaisons
    combined = str(old_nonce) + str(new_nonce)
    test_candidate(combined, f"Nonces combinés: {combined}")

def analyze_special_combinations():
    """Analyse des combinaisons spéciales basées sur les découvertes"""
    print("\n🎯 COMBINAISONS SPÉCIALES")
    print("=" * 25)
    
    # Éléments clés du commit critique
    key_elements = {
        "commit_hash": "2af0908c5a12bac881207ae41805e5b2e1893308",
        "author": "billym2k",
        "timestamp": "1383535347",
        "old_genesis": "c46e3d7f70c0ef730ae2b2963b7c0abf711526bce84f390d4e0be24e9650b557",
        "new_genesis": "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698",
        "nintondo": "4e696e746f6e646f"  # "Nintondo" en hex
    }
    
    # Tester toutes les combinaisons de 2 éléments
    elements = list(key_elements.items())
    for i, (name1, value1) in enumerate(elements):
        for j, (name2, value2) in enumerate(elements):
            if i != j and len(value1) + len(value2) <= 64:
                combined = value1 + value2
                test_candidate(combined, f"{name1} + {name2}")

def main():
    print("🏴‍☠️ ANALYSE DE L'HISTORIQUE GIT POUR LA TREASURE HUNT")
    print("=" * 60)
    
    # 1. Analyser les hashes de commits
    analyze_commit_hashes()
    
    # 2. Analyser les changements critiques
    analyze_critical_commit_changes()
    
    # 3. Analyser les métadonnées auteur
    analyze_author_metadata()
    
    # 4. Analyser les timestamps
    analyze_commit_timestamps()
    
    # 5. Analyser les changements de version
    analyze_version_changes()
    
    # 6. Analyser le message start
    analyze_message_start_pattern()
    
    # 7. Analyser les nonces
    analyze_nonce_patterns()
    
    # 8. Analyser les combinaisons spéciales
    analyze_special_combinations()
    
    print("\n✅ Analyse de l'historique Git terminée!")

if __name__ == "__main__":
    main()

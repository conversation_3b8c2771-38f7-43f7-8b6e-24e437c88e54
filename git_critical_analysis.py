#!/usr/bin/env python3
"""
🏴‍☠️ ANALYSE CRITIQUE DES DÉCOUVERTES GIT
Focus sur les éléments les plus suspects du commit initial de Bellscoin
"""

import hashlib
import struct
from datetime import datetime

# Notre clé publique cible
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_hex_as_key(hex_key):
    """Teste une clé hex avec secp256k1"""
    try:
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        
        # Convertir en bytes
        private_key_bytes = bytes.fromhex(hex_key)
        
        # Vérifier que c'est dans la plage valide pour secp256k1
        private_key_int = int(hex_key, 16)
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Générer la clé publique
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Format non compressé (04 + x + y)
        public_key_hex = "04" + vk.to_string().hex()
        
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            print(f"\n🎉🎉🎉 CLÉ PRIVÉE TROUVÉE! 🎉🎉🎉")
            print(f"Clé privée: {hex_key}")
            print(f"Clé publique: {public_key_hex}")
            return True
            
    except Exception as e:
        pass
    
    return False

def test_candidate(candidate, description):
    """Teste un candidat comme clé privée"""
    print(f"🔍 Test: {description}")
    
    # Nettoyer le candidat
    clean_candidate = str(candidate).strip().lower()
    
    # Tester directement si c'est un hex de 64 caractères
    if len(clean_candidate) == 64 and all(c in '0123456789abcdef' for c in clean_candidate):
        if test_hex_as_key(clean_candidate):
            return True
    
    # Tester comme hash SHA256
    hash_result = hashlib.sha256(str(candidate).encode()).hexdigest()
    if test_hex_as_key(hash_result):
        return True
    
    return False

def analyze_commit_author_signature():
    """Analyse la signature de l'auteur Billy Markus"""
    print("👤 ANALYSE DE LA SIGNATURE BILLY MARKUS")
    print("=" * 40)
    
    # Billy Markus est le créateur de Dogecoin
    # Son commit initial de Bellscoin pourrait contenir des indices
    
    billy_data = {
        "full_name": "Billy Markus",
        "username": "billym2k",
        "email_local": "billym2k",
        "hostname": "billym2k-laptop",
        "domain": "ftrdhcpuser.net",
        "commit_date": "2013-11-03 20:22:27 -0800",
        "commit_timestamp": 1383535347
    }
    
    # Tester des variations du nom
    name_variations = [
        "billymarkus",
        "billy",
        "markus",
        "billym2k",
        "billym2k2013",
        "billym2k1383535347"
    ]
    
    for variation in name_variations:
        test_candidate(variation, f"Variation nom: {variation}")
        
        # Avec Nintondo
        combined = variation + "nintondo"
        if len(combined) <= 64:
            test_candidate(combined, f"Nom + Nintondo: {combined}")

def analyze_critical_timestamp_patterns():
    """Analyse les patterns de timestamps critiques"""
    print("\n⏰ ANALYSE DES TIMESTAMPS CRITIQUES")
    print("=" * 35)
    
    # Timestamps clés du commit initial
    timestamps = {
        "commit_time": 1383535347,      # 2013-11-03 20:22:27 -0800
        "genesis_old_time": 1369197950, # Ancien temps du genesis
        "genesis_new_time": 1383509530, # Nouveau temps du genesis
        "build_time": 1369630407        # Build time original
    }
    
    # Analyser les relations entre timestamps
    for name1, ts1 in timestamps.items():
        for name2, ts2 in timestamps.items():
            if name1 != name2:
                # Différence
                diff = abs(ts1 - ts2)
                test_candidate(str(diff), f"Diff {name1}-{name2}: {diff}")
                
                # Somme
                sum_ts = ts1 + ts2
                test_candidate(str(sum_ts), f"Somme {name1}+{name2}: {sum_ts}")
                
                # XOR
                xor_ts = ts1 ^ ts2
                test_candidate(str(xor_ts), f"XOR {name1}^{name2}: {xor_ts}")
                test_candidate(hex(xor_ts)[2:], f"XOR hex {name1}^{name2}: {hex(xor_ts)[2:]}")

def analyze_genesis_transformation():
    """Analyse la transformation du genesis block"""
    print("\n🔄 ANALYSE DE LA TRANSFORMATION GENESIS")
    print("=" * 40)
    
    # Transformation critique: Nintondo! -> Nintondo
    old_msg = "Nintondo!"
    new_msg = "Nintondo"
    
    # En hex
    old_hex = old_msg.encode().hex()  # 4e696e746f6e646f21
    new_hex = new_msg.encode().hex()  # 4e696e746f6e646f
    
    print(f"Ancien message: {old_msg} -> {old_hex}")
    print(f"Nouveau message: {new_msg} -> {new_hex}")
    
    # La différence est le caractère '!' (0x21)
    diff_char = ord('!')  # 33 en décimal, 0x21 en hex
    
    test_candidate(str(diff_char), f"Caractère différence: {diff_char}")
    test_candidate("21", f"Hex différence: 21")
    
    # Tester des combinaisons avec cette différence
    test_candidate(new_hex + "21", f"Nintondo + différence: {new_hex}21")
    test_candidate("21" + new_hex, f"Différence + Nintondo: 21{new_hex}")

def analyze_nonce_transformation():
    """Analyse la transformation des nonces"""
    print("\n🎯 ANALYSE DE LA TRANSFORMATION NONCE")
    print("=" * 37)
    
    old_nonce = 1197600  # 0x124620
    new_nonce = 44481    # 0xadc1
    
    print(f"Ancien nonce: {old_nonce} (0x{old_nonce:x})")
    print(f"Nouveau nonce: {new_nonce} (0x{new_nonce:x})")
    
    # Analyser la relation mathématique
    ratio = old_nonce / new_nonce  # ~26.9
    diff = old_nonce - new_nonce   # 1153119
    
    test_candidate(str(int(ratio)), f"Ratio nonces: {int(ratio)}")
    test_candidate(str(diff), f"Différence nonces: {diff}")
    test_candidate(hex(diff)[2:], f"Différence hex: {hex(diff)[2:]}")
    
    # Facteurs du nouveau nonce
    # 44481 = 3 × 14827
    factors = [3, 14827, 44481]
    for factor in factors:
        test_candidate(str(factor), f"Facteur de {new_nonce}: {factor}")

def analyze_message_start_transformation():
    """Analyse la transformation du message start"""
    print("\n📡 ANALYSE MESSAGE START TRANSFORMATION")
    print("=" * 40)
    
    old_start = "fbc0b6db"  # Ancien message start
    new_start = "c0c0c0c0"  # Nouveau message start (pattern répétitif)
    
    # Le nouveau pattern c0c0c0c0 est très suspect
    # C'est une répétition parfaite de 0xc0
    
    test_candidate(old_start, f"Ancien message start: {old_start}")
    test_candidate(new_start, f"Nouveau message start: {new_start}")
    
    # Analyser le pattern c0
    c0_value = 0xc0  # 192 en décimal
    test_candidate(str(c0_value), f"Valeur c0: {c0_value}")
    test_candidate("c0", f"Pattern c0")
    
    # XOR entre ancien et nouveau
    old_int = int(old_start, 16)
    new_int = int(new_start, 16)
    xor_result = old_int ^ new_int
    
    test_candidate(hex(xor_result)[2:], f"XOR message start: {hex(xor_result)[2:]}")
    test_candidate(str(xor_result), f"XOR décimal: {xor_result}")

def analyze_hash_transformation():
    """Analyse la transformation des hashes"""
    print("\n🔐 ANALYSE TRANSFORMATION HASHES")
    print("=" * 32)
    
    old_genesis = "c46e3d7f70c0ef730ae2b2963b7c0abf711526bce84f390d4e0be24e9650b557"
    new_genesis = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
    
    # Ces deux hashes sont complètement différents
    # Mais leur transformation pourrait contenir un indice
    
    # XOR entre les deux hashes
    old_int = int(old_genesis, 16)
    new_int = int(new_genesis, 16)
    xor_result = old_int ^ new_int
    xor_hex = format(xor_result, '064x')
    
    test_candidate(xor_hex, f"XOR genesis hashes: {xor_hex}")
    
    # Analyser des chunks des hashes
    for i in range(0, 64, 8):
        old_chunk = old_genesis[i:i+8]
        new_chunk = new_genesis[i:i+8]
        
        test_candidate(old_chunk, f"Ancien chunk {i//8}: {old_chunk}")
        test_candidate(new_chunk, f"Nouveau chunk {i//8}: {new_chunk}")
        
        # XOR des chunks
        if len(old_chunk) == 8 and len(new_chunk) == 8:
            chunk_xor = int(old_chunk, 16) ^ int(new_chunk, 16)
            test_candidate(format(chunk_xor, '08x'), f"XOR chunk {i//8}: {format(chunk_xor, '08x')}")

def analyze_billy_markus_dogecoin_connection():
    """Analyse la connexion Billy Markus - Dogecoin"""
    print("\n🐕 ANALYSE CONNEXION DOGECOIN")
    print("=" * 28)
    
    # Billy Markus a créé Dogecoin le 6 décembre 2013
    # Bellscoin a été créé le 3 novembre 2013 (avant Dogecoin!)
    
    dogecoin_data = {
        "launch_date": "2013-12-06",
        "launch_timestamp": 1386288000,  # Approximatif
        "meme_origin": "doge",
        "original_name": "dogecoin",
        "much_wow": "muchwow",
        "such_crypto": "suchcrypto"
    }
    
    for key, value in dogecoin_data.items():
        test_candidate(str(value), f"Dogecoin {key}: {value}")
        
        # Combinaisons avec Nintondo
        if isinstance(value, str) and len(value) + 8 <= 32:  # 8 = len("nintondo")
            combined = value + "nintondo"
            test_candidate(combined, f"Dogecoin {key} + Nintondo: {combined}")

def analyze_final_critical_combinations():
    """Analyse finale des combinaisons les plus critiques"""
    print("\n🎯 COMBINAISONS CRITIQUES FINALES")
    print("=" * 33)
    
    # Éléments les plus suspects identifiés
    critical_elements = {
        "billym2k": "62696c6c796d326b",
        "nintondo": "4e696e746f6e646f",
        "commit_ts": "527712f3",  # 1383535347 en hex
        "new_nonce": "adc1",      # 44481 en hex
        "msg_start": "c0c0c0c0",
        "exclamation": "21",      # Différence Nintondo! vs Nintondo
        "c0_pattern": "c0",
        "diff_nonce": "119a5f"    # 1153119 en hex (diff des nonces)
    }
    
    # Tester toutes les combinaisons de 2-3 éléments
    elements = list(critical_elements.items())
    
    for i, (name1, val1) in enumerate(elements):
        for j, (name2, val2) in enumerate(elements):
            if i != j and len(val1) + len(val2) <= 64:
                combined = val1 + val2
                test_candidate(combined, f"CRITIQUE: {name1} + {name2}")
                
                # Combinaisons de 3 éléments (les plus courtes)
                for k, (name3, val3) in enumerate(elements):
                    if k != i and k != j and len(val1) + len(val2) + len(val3) <= 64:
                        triple = val1 + val2 + val3
                        test_candidate(triple, f"TRIPLE: {name1}+{name2}+{name3}")

def main():
    print("🏴‍☠️ ANALYSE CRITIQUE DES DÉCOUVERTES GIT")
    print("=" * 50)
    
    # 1. Analyser la signature de Billy Markus
    analyze_commit_author_signature()
    
    # 2. Analyser les patterns de timestamps
    analyze_critical_timestamp_patterns()
    
    # 3. Analyser la transformation du genesis
    analyze_genesis_transformation()
    
    # 4. Analyser la transformation des nonces
    analyze_nonce_transformation()
    
    # 5. Analyser la transformation du message start
    analyze_message_start_transformation()
    
    # 6. Analyser la transformation des hashes
    analyze_hash_transformation()
    
    # 7. Analyser la connexion Dogecoin
    analyze_billy_markus_dogecoin_connection()
    
    # 8. Analyse finale des combinaisons critiques
    analyze_final_critical_combinations()
    
    print("\n✅ Analyse critique terminée!")
    print("🔍 Focus sur les éléments les plus suspects du commit initial")

if __name__ == "__main__":
    main()
